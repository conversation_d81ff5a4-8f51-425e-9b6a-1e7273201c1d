import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, Palette, Type, Hash, Eye, EyeOff } from 'lucide-react'
import { Theme, CreateThemeRequest, UpdateThemeRequest } from '../../services/curatedService'
import { cn } from '../../utils/cn'

interface ThemeModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (themeData: CreateThemeRequest | UpdateThemeRequest) => Promise<void>
  theme?: Theme | null
  loading?: boolean
}

const ThemeModal: React.FC<ThemeModalProps> = ({
  isOpen,
  onClose,
  onSave,
  theme,
  loading = false
}) => {
  const [formData, setFormData] = useState<CreateThemeRequest>({
    name: '',
    name_en: '',
    description: '',
    description_en: '',
    category: '',
    icon: '🎨',
    background_color: '#4ECDC4',
    font_color: '#FFFFFF',
    is_active: true
  })

  const [showPreview, setShowPreview] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Reset form when modal opens/closes or theme changes
  useEffect(() => {
    if (isOpen) {
      if (theme) {
        // Edit mode
        setFormData({
          name: theme.name || '',
          name_en: theme.name_en || '',
          description: theme.description || '',
          description_en: theme.description_en || '',
          category: theme.category || '',
          icon: theme.icon || '🎨',
          background_color: theme.background_color || theme.color || '#4ECDC4',
          font_color: theme.font_color || '#FFFFFF',
          is_active: theme.is_active ?? true
        })
      } else {
        // Create mode
        setFormData({
          name: '',
          name_en: '',
          description: '',
          description_en: '',
          category: '',
          icon: '🎨',
          background_color: '#4ECDC4',
          font_color: '#FFFFFF',
          is_active: true
        })
      }
      setErrors({})
    }
  }, [isOpen, theme])

  const handleInputChange = (field: keyof CreateThemeRequest, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.name_en.trim()) newErrors.name_en = 'English name is required'
    if (!formData.description.trim()) newErrors.description = 'Description is required'
    if (!formData.description_en.trim()) newErrors.description_en = 'English description is required'
    if (!formData.category.trim()) newErrors.category = 'Category is required'
    if (!formData.icon.trim()) newErrors.icon = 'Icon is required'
    if (!formData.background_color.trim()) newErrors.background_color = 'Background color is required'
    if (!formData.font_color.trim()) newErrors.font_color = 'Font color is required'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving theme:', error)
    }
  }

  const commonInputClasses = "w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-2xl mx-4 bg-background border border-border rounded-xl shadow-xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                <Palette className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-foreground">
                {theme ? 'Edit Theme' : 'Create New Theme'}
              </h2>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent"
              >
                {showPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
              <button
                onClick={onClose}
                className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              {showPreview && (
                <div className="mb-6 p-4 border border-border rounded-lg">
                  <h3 className="text-sm font-medium text-foreground mb-3">Preview</h3>
                  <div
                    className="p-4 rounded-lg text-center"
                    style={{
                      backgroundColor: formData.background_color,
                      color: formData.font_color
                    }}
                  >
                    <div className="text-2xl mb-2">{formData.icon}</div>
                    <div className="font-medium">{formData.name_en || 'Theme Name'}</div>
                    <div className="text-sm opacity-80">{formData.category || 'Category'}</div>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className={cn(commonInputClasses, errors.name && 'border-red-500')}
                      placeholder="Theme name"
                    />
                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      English Name
                    </label>
                    <input
                      type="text"
                      value={formData.name_en}
                      onChange={(e) => handleInputChange('name_en', e.target.value)}
                      className={cn(commonInputClasses, errors.name_en && 'border-red-500')}
                      placeholder="English theme name"
                    />
                    {errors.name_en && <p className="text-red-500 text-xs mt-1">{errors.name_en}</p>}
                  </div>
                </div>

                {/* Descriptions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      className={cn(commonInputClasses, errors.description && 'border-red-500')}
                      placeholder="Theme description"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      English Description
                    </label>
                    <textarea
                      value={formData.description_en}
                      onChange={(e) => handleInputChange('description_en', e.target.value)}
                      className={cn(commonInputClasses, errors.description_en && 'border-red-500')}
                      placeholder="English theme description"
                      rows={3}
                    />
                    {errors.description_en && <p className="text-red-500 text-xs mt-1">{errors.description_en}</p>}
                  </div>
                </div>

                {/* Category and Icon */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Category
                    </label>
                    <input
                      type="text"
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className={cn(commonInputClasses, errors.category && 'border-red-500')}
                      placeholder="Theme category"
                    />
                    {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Icon (Emoji)
                    </label>
                    <input
                      type="text"
                      value={formData.icon}
                      onChange={(e) => handleInputChange('icon', e.target.value)}
                      className={cn(commonInputClasses, errors.icon && 'border-red-500')}
                      placeholder="🎨"
                      maxLength={4}
                    />
                    {errors.icon && <p className="text-red-500 text-xs mt-1">{errors.icon}</p>}
                  </div>
                </div>

                {/* Colors */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Background Color
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        value={formData.background_color}
                        onChange={(e) => handleInputChange('background_color', e.target.value)}
                        className="w-12 h-10 border border-border rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.background_color}
                        onChange={(e) => handleInputChange('background_color', e.target.value)}
                        className={cn(commonInputClasses, errors.background_color && 'border-red-500', 'flex-1')}
                        placeholder="#4ECDC4"
                      />
                    </div>
                    {errors.background_color && <p className="text-red-500 text-xs mt-1">{errors.background_color}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Font Color
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        value={formData.font_color}
                        onChange={(e) => handleInputChange('font_color', e.target.value)}
                        className="w-12 h-10 border border-border rounded cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.font_color}
                        onChange={(e) => handleInputChange('font_color', e.target.value)}
                        className={cn(commonInputClasses, errors.font_color && 'border-red-500', 'flex-1')}
                        placeholder="#FFFFFF"
                      />
                    </div>
                    {errors.font_color && <p className="text-red-500 text-xs mt-1">{errors.font_color}</p>}
                  </div>
                </div>

                {/* Active Status */}
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="is_active"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="w-4 h-4 text-primary border-border rounded focus:ring-primary/20"
                  />
                  <label htmlFor="is_active" className="text-sm font-medium text-foreground">
                    Active Theme
                  </label>
                </div>
              </form>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {theme ? 'Update Theme' : 'Create Theme'}
                </>
              )}
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default ThemeModal
