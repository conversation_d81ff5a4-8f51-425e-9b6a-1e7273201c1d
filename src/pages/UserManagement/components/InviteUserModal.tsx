import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, UserPlus, Co<PERSON>, Check, AlertCircle, ExternalLink } from 'lucide-react'
import userManagementService, { InviteUserRequest } from '../../../services/userManagement/userManagementService'
import { cn } from '../../../utils/cn'

interface InviteUserModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

/**
 * Invite User Modal - Create invitation tokens for new users
 */
const InviteUserModal: React.FC<InviteUserModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<InviteUserRequest>({
    username: '',
    role: 'agent'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [invitationToken, setInvitationToken] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [countdown, setCountdown] = useState(5)
  const [autoRedirect, setAutoRedirect] = useState(false)

  // Auto-redirect countdown effect
  useEffect(() => {
    if (invitationToken && autoRedirect && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(prev => prev - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else if (invitationToken && autoRedirect && countdown === 0) {
      navigateToRegister()
    }
  }, [invitationToken, autoRedirect, countdown])

  // Navigate to registration page with token
  const navigateToRegister = () => {
    if (invitationToken) {
      const registerUrl = `${window.location.origin}/register?token=${invitationToken}`
      window.open(registerUrl, '_blank')
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await userManagementService.inviteUser(formData)
      
      if (response.success) {
        setInvitationToken(response.registration_token)
        setSuccess(response.msg)
        setAutoRedirect(true) // Start auto-redirect countdown
        onSuccess?.()
      } else {
        setError(response.msg || 'Failed to create invitation')
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create invitation')
    } finally {
      setLoading(false)
    }
  }

  // Copy token to clipboard
  const copyToken = async () => {
    if (invitationToken) {
      try {
        await navigator.clipboard.writeText(invitationToken)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (err) {
        console.error('Failed to copy token:', err)
      }
    }
  }

  // Reset modal state
  const resetModal = () => {
    setFormData({ username: '', role: 'agent' })
    setError(null)
    setSuccess(null)
    setInvitationToken(null)
    setCopied(false)
    setCountdown(5)
    setAutoRedirect(false)
  }

  // Handle close
  const handleClose = () => {
    resetModal()
    onClose()
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-md bg-card border border-border rounded-2xl shadow-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-border">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <UserPlus className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">Invite User</h2>
                  <p className="text-sm text-muted-foreground">Create an invitation for a new user</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-muted rounded-lg transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {!invitationToken ? (
                /* Invitation Form */
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Username */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Username
                    </label>
                    <input
                      type="text"
                      value={formData.username}
                      onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                      placeholder="Enter username"
                      required
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    />
                  </div>

                  {/* Role */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Role
                    </label>
                    <select
                      value={formData.role}
                      onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value as any }))}
                      className="w-full px-3 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    >
                      <option value="agent">Agent</option>
                      <option value="supervisor">Supervisor</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-destructive flex-shrink-0" />
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  )}

                  {/* Submit Button */}
                  <div className="flex gap-3 pt-2">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="flex-1 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading || !formData.username.trim()}
                      className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Creating...' : 'Create Invitation'}
                    </button>
                  </div>
                </form>
              ) : (
                /* Success State with Token */
                <div className="space-y-4">
                  {/* Success Message */}
                  <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <Check className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0" />
                    <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
                  </div>

                  {/* Token Display */}
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2">
                      Invitation Token
                    </label>
                    <div className="relative">
                      <textarea
                        value={invitationToken}
                        readOnly
                        rows={4}
                        className="w-full px-3 py-2 bg-muted border border-border rounded-lg text-sm font-mono resize-none"
                      />
                      <button
                        onClick={copyToken}
                        className={cn(
                          "absolute top-2 right-2 p-2 rounded-lg transition-colors",
                          copied
                            ? "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400"
                            : "bg-background hover:bg-muted border border-border"
                        )}
                      >
                        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {/* Instructions */}
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Share this token with the user. They can use it to register at:
                    </p>
                    <code className="text-xs text-blue-600 dark:text-blue-400 mt-1 block">
                      {window.location.origin}/register?token={invitationToken}
                    </code>
                  </div>

                  {/* Auto-redirect notification */}
                  {autoRedirect && countdown > 0 && (
                    <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                      <p className="text-sm text-amber-700 dark:text-amber-300">
                        Opening registration page in new tab in {countdown} seconds...
                      </p>
                      <button
                        onClick={() => setAutoRedirect(false)}
                        className="text-xs text-amber-600 dark:text-amber-400 hover:underline mt-1"
                      >
                        Cancel auto-redirect
                      </button>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <button
                      onClick={navigateToRegister}
                      className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Open Registration Page
                    </button>
                    <button
                      onClick={handleClose}
                      className="flex-1 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      Done
                    </button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}

export default InviteUserModal
