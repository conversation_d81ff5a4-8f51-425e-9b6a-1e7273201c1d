import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { 
  Users as UsersIcon, 
  UserPlus, 
  Search, 
  Filter, 
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Calendar,
  Shield,
  User,
  Mail
} from 'lucide-react'
import MainLayout from '../../components/layout/MainLayout'
import userManagementService, { User as UserType, PaginationParams } from '../../services/userManagement/userManagementService'
import InviteUserModal from './components/InviteUserModal'
import { cn } from '../../utils/cn'

/**
 * Users List Page - Display and manage users with pagination
 */
const Users: React.FC = () => {
  const [users, setUsers] = useState<UserType[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('')

  // Modal state
  const [showInviteModal, setShowInviteModal] = useState(false)
  
  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  const staggerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  // Load users data
  const loadUsers = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const params: PaginationParams = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        role: roleFilter || undefined
      }
      
      const response = await userManagementService.getUsers(params)
      setUsers(response.data)
      setTotalCount(response.meta.total)
    } catch (err: any) {
      setError(err.message || 'Failed to load users')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchTerm, roleFilter])

  // Initial load
  useEffect(() => {
    loadUsers()
  }, [loadUsers])

  // Handle search
  const handleSearch = useCallback((value: string) => {
    setSearchTerm(value)
    setCurrentPage(1) // Reset to first page
  }, [])

  // Handle role filter
  const handleRoleFilter = useCallback((role: string) => {
    setRoleFilter(role)
    setCurrentPage(1) // Reset to first page
  }, [])

  // Pagination calculations
  const totalPages = Math.ceil(totalCount / pageSize)
  const startIndex = (currentPage - 1) * pageSize + 1
  const endIndex = Math.min(currentPage * pageSize, totalCount)

  // Role badge styling
  const getRoleBadge = (role: string) => {
    const styles = {
      admin: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
      supervisor: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      agent: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
    }
    
    return (
      <span className={cn(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
        styles[role as keyof typeof styles] || 'bg-gray-100 text-gray-800'
      )}>
        <Shield className="w-3 h-3 mr-1" />
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    )
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <MainLayout
      title="User Management"
      description="Manage users, roles, and invitations"
    >
      <div className="h-full flex flex-col">

        {/* Fixed Header - Search and Actions */}
        <div className="flex-shrink-0 p-6 border-b border-border bg-background/95 backdrop-blur-sm">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">

            {/* Left Side - Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-3 flex-1 max-w-2xl">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search users by name, email, or username..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-background border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
                />
              </div>

              {/* Role Filter */}
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <select
                  value={roleFilter}
                  onChange={(e) => handleRoleFilter(e.target.value)}
                  className="pl-10 pr-8 py-3 bg-background border border-border rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary appearance-none min-w-[140px] transition-all"
                >
                  <option value="">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="supervisor">Supervisor</option>
                  <option value="agent">Agent</option>
                </select>
              </div>
            </div>

            {/* Right Side - User Count and Actions */}
            <div className="flex items-center gap-4">
              {/* User Count */}
              <div className="flex items-center gap-2 px-4 py-2 bg-muted/50 rounded-xl">
                <UsersIcon className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-foreground">
                  {totalCount} Users
                </span>
              </div>

              {/* Refresh Button */}
              <button
                onClick={loadUsers}
                disabled={loading}
                className="p-3 hover:bg-muted rounded-xl transition-colors group"
                title="Refresh users"
              >
                <RefreshCw className={cn(
                  "h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500",
                  loading && "animate-spin"
                )} />
              </button>

              {/* Invite User Button */}
              <motion.button
                onClick={() => setShowInviteModal(true)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-colors font-medium shadow-lg"
              >
                <UserPlus className="h-4 w-4" />
                Invite User
              </motion.button>
            </div>
          </div>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center space-y-3">
                  <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto" />
                  <p className="text-muted-foreground">Loading users...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center space-y-3">
                  <p className="text-destructive font-medium">Failed to load users</p>
                  <p className="text-sm text-muted-foreground">{error}</p>
                  <button
                    onClick={loadUsers}
                    className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            ) : !users || users.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center space-y-3">
                  <UsersIcon className="h-12 w-12 text-muted-foreground mx-auto" />
                  <p className="text-muted-foreground">No users found</p>
                </div>
              </div>
            ) : (
              /* Users Grid - Card Layout */
              <motion.div
                variants={staggerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
              >
                {users?.map((user) => (
                  <motion.div
                    key={user.id}
                    variants={cardVariants}
                    whileHover={{ y: -4, scale: 1.02 }}
                    className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden shadow-lg backdrop-blur-sm hover:shadow-xl transition-all duration-300"
                  >
                    {/* Card Background Animation */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-100/20 via-purple-50/10 to-indigo-100/20 dark:from-blue-900/10 dark:via-purple-900/5 dark:to-indigo-900/10 rounded-2xl animate-pulse" style={{ animationDuration: '6s' }} />

                    <div className="relative space-y-4">
                      {/* User Avatar and Basic Info */}
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                            <User className="h-6 w-6 text-primary" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-foreground text-lg">
                              {user.full_name || user.username}
                            </h3>
                            <p className="text-sm text-muted-foreground">@{user.username}</p>
                          </div>
                        </div>

                        {/* Actions Menu */}
                        <button className="p-2 hover:bg-muted/50 rounded-lg transition-colors">
                          <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
                        </button>
                      </div>

                      {/* Email */}
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span className="truncate">{user.email}</span>
                      </div>

                      {/* Role and Status */}
                      <div className="flex items-center justify-between">
                        {getRoleBadge(user.role)}
                        <span className={cn(
                          'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium',
                          user.onboarding_completed
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        )}>
                          {user.onboarding_completed ? 'Active' : 'Pending'}
                        </span>
                      </div>

                      {/* Dates */}
                      <div className="space-y-2 pt-2 border-t border-border/50">
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Created
                          </span>
                          <span>{formatDate(user.created_at)}</span>
                        </div>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>Last Login</span>
                          <span>{user.last_login ? formatDate(user.last_login) : 'Never'}</span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>

        {/* Fixed Pagination Footer */}
        <div className="flex-shrink-0 border-t border-border bg-background/95 backdrop-blur-sm p-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {startIndex} to {endIndex} of {totalCount} users
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="p-2 hover:bg-muted rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={cn(
                        'px-3 py-2 rounded-xl text-sm transition-colors font-medium',
                        currentPage === page
                          ? 'bg-primary text-primary-foreground shadow-lg'
                          : 'hover:bg-muted'
                      )}
                    >
                      {page}
                    </button>
                  )
                })}
              </div>

              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="p-2 hover:bg-muted rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Invite User Modal */}
      <InviteUserModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        onSuccess={() => {
          loadUsers() // Refresh users list
          setShowInviteModal(false)
        }}
      />
    </MainLayout>
  )
}

export default Users
