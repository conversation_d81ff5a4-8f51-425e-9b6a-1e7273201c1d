import React, { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { TrendingUp } from 'lucide-react'
import * as echarts from 'echarts'
import type { UsersMetrics } from '../../../services/management/managementService'

interface UserAnalyticsCardProps {
  data: UsersMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const UserAnalyticsCard: React.FC<UserAnalyticsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    if (data?.daily_data) {
      run(data.daily_data)
    }

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
      chartInstance.current = null
    }
  }, [data])

  // Resize chart when container size changes
  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      chartInstance.current?.resize()
    })

    if (chartRef.current) {
      resizeObserver.observe(chartRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  function run(_rawData: any) {
    if (!chartInstance.current) return

    console.log('UserAnalyticsCard: Running race chart with data:', _rawData)

    // Transform API data to race chart format
    const registrations = _rawData.registrations || []
    const activeUsers = _rawData.active_users || []

    // Get all unique dates and sort them
    const allDates = new Set([
      ...registrations.map((item: any) => item._id),
      ...activeUsers.map((item: any) => item._id)
    ])
    const sortedDates = Array.from(allDates).sort()

    // Create race data format
    const raceData: any[] = []
    const regMap = new Map(registrations.map((item: any) => [item._id, item.count]))
    const activeMap = new Map(activeUsers.map((item: any) => [item._id, item.count]))

    sortedDates.forEach(date => {
      const formattedDate = new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })

      raceData.push({
        Date: formattedDate,
        Count: regMap.get(date) || 0,
        Series: 'New Registrations'
      })

      raceData.push({
        Date: formattedDate,
        Count: activeMap.get(date) || 0,
        Series: 'Active Users'
      })
    })

    // Race chart setup based on your example
    const series = ['New Registrations', 'Active Users']
    const datasetWithFilters: any[] = []
    const seriesList: any[] = []

    echarts.util.each(series, function (seriesName) {
      const datasetId = 'dataset_' + seriesName.replace(/\s+/g, '_')

      datasetWithFilters.push({
        id: datasetId,
        fromDatasetId: 'dataset_raw',
        transform: {
          type: 'filter',
          config: {
            and: [
              { dimension: 'Series', '=': seriesName }
            ]
          }
        }
      })

      seriesList.push({
        type: 'line',
        datasetId: datasetId,
        showSymbol: true,
        name: seriesName,
        smooth: true,
        symbol: seriesName === 'New Registrations' ? 'circle' : 'diamond',
        symbolSize: 8,
        lineStyle: {
          width: 4,
          color: seriesName === 'New Registrations' ? '#10b981' : '#8b5cf6'
        },
        itemStyle: {
          color: seriesName === 'New Registrations' ? '#10b981' : '#8b5cf6',
          borderColor: '#ffffff',
          borderWidth: 2
        },
        areaStyle: {
          color: seriesName === 'New Registrations'
            ? 'rgba(16, 185, 129, 0.2)'
            : 'rgba(139, 92, 246, 0.2)'
        },
        endLabel: {
          show: true,
          formatter: function (params: any) {
            // For ECharts dataset transform, the data structure is different
            // params.value is an array where:
            // [0] = Date, [1] = Count, [2] = Series
            if (Array.isArray(params.value) && params.value.length >= 2) {
              const count = params.value[1] || 0
              return `${seriesName}: ${count}`
            }

            // Handle object case
            if (typeof params.value === 'object' && params.value !== null) {
              const count = params.value.Count || params.value.count || 0
              return `${seriesName}: ${count}`
            }

            // Fallback for primitive values
            return `${seriesName}: ${params.value || 0}`
          },
          color: seriesName === 'New Registrations' ? '#10b981' : '#8b5cf6',
          fontWeight: 'bold'
        },
        labelLayout: {
          moveOverlap: 'shiftY'
        },
        emphasis: {
          focus: 'series'
        },
        encode: {
          x: 'Date',
          y: 'Count',
          label: ['Series', 'Count'],
          itemName: 'Date',
          tooltip: ['Count']
        }
      })
    })

    const option = {
      animationDuration: 3000, // 3 seconds race animation
      dataset: [
        {
          id: 'dataset_raw',
          source: raceData
        },
        ...datasetWithFilters
      ],
      title: {
        text: 'User Trends Race',
        textStyle: {
          color: '#374151',
          fontSize: 16,
          fontWeight: 'bold'
        },
        left: 'center',
        top: 10
      },
      tooltip: {
        order: 'valueDesc',
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: { color: '#374151', fontSize: 12 },
        formatter: function (params: any) {
          if (!Array.isArray(params)) return ''

          let result = `<div style="font-weight: bold; margin-bottom: 4px;">${params[0]?.axisValue || ''}</div>`

          params.forEach((param: any) => {
            let value = 0
            if (Array.isArray(param.value) && param.value.length >= 2) {
              value = param.value[1] || 0
            } else if (typeof param.value === 'object' && param.value !== null) {
              value = param.value.Count || param.value.count || 0
            } else {
              value = param.value || 0
            }

            result += `<div style="margin: 2px 0;">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${param.seriesName}: <strong>${value}</strong>
            </div>`
          })

          return result
        }
      },
      legend: {
        data: series,
        top: 40,
        left: 'center',
        textStyle: {
          color: '#6b7280',
          fontSize: 12
        }
      },
      xAxis: {
        type: 'category',
        nameLocation: 'middle',
        axisLine: {
          lineStyle: { color: '#e5e7eb', width: 1 }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11
        }
      },
      yAxis: {
        name: 'Count',
        nameTextStyle: {
          color: '#6b7280',
          fontSize: 11
        },
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6',
            width: 1,
            type: 'dashed'
          }
        }
      },
      grid: {
        right: 60,
        left: 40,
        top: 60,
        bottom: 40,
        containLabel: true
      },
      series: seriesList
    }

    chartInstance.current.setOption(option)
    console.log('UserAnalyticsCard: Race chart updated successfully')
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              User Trends Race
            </h3>
            <p className="text-sm text-muted-foreground">Animated racing line chart</p>
          </div>
        </div>

        <div className="flex-1 min-h-0">
          <div
            ref={chartRef}
            style={{ height: '100%', width: '100%', minHeight: '380px' }}
          />
        </div>
      </div>
    </motion.div>
  )
}

export default UserAnalyticsCard