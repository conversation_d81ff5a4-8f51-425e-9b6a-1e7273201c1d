import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar, Filter, RotateCcw, ChevronDown } from 'lucide-react'
import { cn } from '../../../utils/cn'

interface TopDateFilterProps {
  startDate: string
  endDate: string
  onDateChange: (startDate: string, endDate: string) => void
  onReset: () => void
  cardVariants: any
}

/**
 * Top Date Filter - Single line date filter for dashboard
 * Professional design matching the reference image
 */
const TopDateFilter: React.FC<TopDateFilterProps> = ({
  startDate,
  endDate,
  onDateChange,
  onReset,
  cardVariants
}) => {
  const [showCustom, setShowCustom] = useState(false)

  // Preset date ranges
  const presets = [
    {
      label: 'Today',
      getValue: () => {
        const today = new Date().toISOString().split('T')[0]
        return { start: today, end: today }
      }
    },
    {
      label: 'Last 7 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'Last 30 Days',
      getValue: () => {
        const end = new Date().toISOString().split('T')[0]
        const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        return { start, end }
      }
    },
    {
      label: 'This Month',
      getValue: () => {
        const now = new Date()
        const start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        const end = new Date().toISOString().split('T')[0]
        return { start, end }
      }
    }
  ]

  const handlePresetClick = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    onDateChange(start, end)
    setShowCustom(false)
  }

  const handleCustomDateChange = (field: 'start' | 'end', value: string) => {
    if (field === 'start') {
      onDateChange(value, endDate)
    } else {
      onDateChange(startDate, value)
    }
  }

  const isPresetActive = (preset: typeof presets[0]) => {
    const { start, end } = preset.getValue()
    return startDate === start && endDate === end
  }

  const formatDateRange = () => {
    const start = new Date(startDate).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
    const end = new Date(endDate).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    })
    return `${start} - ${end}`
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden shadow-xl backdrop-blur-sm"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative"
    >
      <div className="flex items-center justify-between flex-wrap gap-4">
        {/* Left side - Title and current range */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-foreground">Date Range</h3>
              <p className="text-xs text-muted-foreground">{formatDateRange()}</p>
            </div>
          </div>
        </div>

        {/* Center - Preset buttons */}
        <div className="flex items-center gap-2 flex-wrap">
          {presets.map((preset) => (
            <motion.button
              key={preset.label}
              onClick={() => handlePresetClick(preset)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={cn(
                "px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200",
                isPresetActive(preset)
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
              )}
            >
              {preset.label}
            </motion.button>
          ))}
          
          {/* Custom Range Toggle */}
          <motion.button
            onClick={() => setShowCustom(!showCustom)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={cn(
              "px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 flex items-center gap-1",
              showCustom
                ? "bg-secondary text-secondary-foreground"
                : "bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground"
            )}
          >
            <Calendar className="h-3 w-3" />
            Custom
            <ChevronDown className={cn(
              "h-3 w-3 transition-transform duration-200",
              showCustom && "rotate-180"
            )} />
          </motion.button>
        </div>

        {/* Right side - Reset button */}
        <motion.button
          onClick={onReset}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="p-2 hover:bg-muted rounded-md transition-colors"
          title="Reset to default"
        >
          <RotateCcw className="h-4 w-4 text-muted-foreground" />
        </motion.button>
      </div>

      {/* Custom Date Inputs */}
      <motion.div
        initial={false}
        animate={{ 
          height: showCustom ? 'auto' : 0, 
          opacity: showCustom ? 1 : 0,
          marginTop: showCustom ? 16 : 0
        }}
        transition={{ duration: 0.2 }}
        className="overflow-hidden"
      >
        <div className="flex items-center gap-4 pt-4 border-t border-border">
          <div className="flex items-center gap-2">
            <label className="text-xs font-medium text-muted-foreground">
              From:
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => handleCustomDateChange('start', e.target.value)}
              className="px-3 py-1.5 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="text-xs font-medium text-muted-foreground">
              To:
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => handleCustomDateChange('end', e.target.value)}
              className="px-3 py-1.5 text-xs bg-background border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TopDateFilter
