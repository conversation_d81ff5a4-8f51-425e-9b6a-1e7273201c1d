import React from 'react'
import { motion } from 'framer-motion'
import MainLayout from '../../components/layout/MainLayout'
import LeaderboardCard from './sub-components/LeaderboardCard'
import OverviewMetricsCard from './sub-components/OverviewMetricsCard'
import UserAnalyticsCard from './sub-components/UserAnalyticsCard'
import TaskSetsAnalyticsCard from './sub-components/TaskSetsAnalyticsCard'

import TopDateFilter from './sub-components/TopDateFilter'
import useManagementDashboard from './ManagementDashboard.container'
import { useLeaderboard } from '../../hooks/useLearningStats'

/**
 * Management Dashboard Component - Modern admin dashboard with sleek animations
 * Features overview metrics, user analytics, task sets analytics, and date filtering
 */
const ManagementDashboard: React.FC = () => {
  const {
    // State
    overview,
    overviewLoading,
    overviewError,
    usersMetrics,
    usersLoading,
    usersError,
    taskSetsMetrics,
    taskSetsLoading,
    taskSetsError,
    startDate,
    endDate,
    user,
    
    // Actions
    handleDateChange,
    handleDateReset,
    refreshOverview,
    refreshUsersMetrics,
    refreshTaskSetsMetrics
  } = useManagementDashboard()

  // Get leaderboard data (keeping existing functionality)
  const {
    leaderboard,
    loading: leaderboardLoading,
    error: leaderboardError,
    refetch: refreshLeaderboard
  } = useLeaderboard({ skip: 0, limit: 10 })

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94] as const
      }
    }
  }



  const staggerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  }

  return (
    <MainLayout
      title="Management Dashboard"
      description={`Admin Overview - Welcome back, ${user?.full_name || user?.username}!`}
    >
      {/* Main Container with proper height calculation */}
      <div className="h-[calc(100vh-4rem)] flex flex-col">

        {/* Top Date Filter - Fixed at top */}
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          className="flex-shrink-0 p-4 border-b border-border bg-background/95 backdrop-blur-sm"
        >
          <TopDateFilter
            startDate={startDate}
            endDate={endDate}
            onDateChange={handleDateChange}
            onReset={handleDateReset}
            cardVariants={cardVariants}
          />
        </motion.div>

        {/* Main Content Grid - Proper height distribution */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-0 min-h-0 overflow-hidden">

          {/* Left Side Content - 75% width, scrollable */}
          <div className="lg:col-span-3 flex flex-col min-h-0 overflow-hidden">

            {/* Overview Metrics - Fixed height, no scroll */}
            <motion.div
              variants={cardVariants}
              initial="hidden"
              animate="visible"
              className="flex-shrink-0 p-4 border-b border-border/50"
            >
              <OverviewMetricsCard
                data={overview}
                taskSetsData={taskSetsMetrics}
                loading={overviewLoading}
                error={overviewError}
                onRefresh={refreshOverview}
                cardVariants={cardVariants}
              />
            </motion.div>

            {/* Charts Container - Scrollable area with proper spacing */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden">
              <div className="p-4 space-y-6">

                {/* User Analytics - Race Chart with fixed height */}
                <motion.div
                  variants={staggerVariants}
                  initial="hidden"
                  animate="visible"
                  className="w-full"
                >
                  <motion.div
                    variants={cardVariants}
                    className="h-[450px] w-full"
                  >
                    <UserAnalyticsCard
                      data={usersMetrics}
                      loading={usersLoading}
                      error={usersError}
                      onRefresh={refreshUsersMetrics}
                      cardVariants={cardVariants}
                    />
                  </motion.div>
                </motion.div>

                {/* Task Sets Analytics - Sunburst Chart with fixed height */}
                <motion.div
                  variants={staggerVariants}
                  initial="hidden"
                  animate="visible"
                  className="w-full"
                >
                  <motion.div
                    variants={cardVariants}
                    className="h-[550px] w-full"
                  >
                    <TaskSetsAnalyticsCard
                      data={taskSetsMetrics}
                      loading={taskSetsLoading}
                      error={taskSetsError}
                      onRefresh={refreshTaskSetsMetrics}
                      cardVariants={cardVariants}
                    />
                  </motion.div>
                </motion.div>

                {/* Bottom padding for better scrolling */}
                <div className="h-4"></div>

              </div>
            </div>
          </div>

          {/* Right Side - Fixed Leaderboard (25% width, no scrolling) */}
          <motion.div
            className="lg:col-span-1 border-l border-border bg-background/50 backdrop-blur-sm"
            variants={cardVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="h-full overflow-hidden">
              <div className="p-4 h-full">
                <LeaderboardCard
                  leaderboard={leaderboard}
                  loading={leaderboardLoading}
                  error={leaderboardError}
                  onRefresh={refreshLeaderboard}
                  cardVariants={cardVariants}
                />
              </div>
            </div>
          </motion.div>

        </div>
      </div>
    </MainLayout>
  )
}

export default ManagementDashboard
