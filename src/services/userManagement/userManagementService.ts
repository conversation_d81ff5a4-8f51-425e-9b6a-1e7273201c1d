import httpService from '../http/httpService'

// Types for User Management
export interface User {
  id: string
  username: string
  full_name: string
  email: string
  role: 'admin' | 'supervisor' | 'agent'
  created_at: string
  last_login: string | null
  onboarding_completed: boolean
}

export interface UsersListResponse {
  users: User[]
  total_count: number
}

export interface InviteUserRequest {
  username: string
  role: 'admin' | 'supervisor' | 'agent'
}

export interface InviteUserResponse {
  registration_token: string
  success: boolean
  msg: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  role?: string
}

/**
 * User Management Service
 * Handles all user-related API operations for admin dashboard
 */
class UserManagementService {
  private baseUrl = '/v1/auth'

  /**
   * Get list of users with pagination and filtering
   */
  async getUsers(params: PaginationParams = {}): Promise<UsersListResponse> {
    const queryParams = new URLSearchParams()
    
    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.role) queryParams.append('role', params.role)

    const url = `${this.baseUrl}/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    
    const response = await httpService.get<UsersListResponse>(url)
    return response.data
  }

  /**
   * Invite a new user
   */
  async inviteUser(data: InviteUserRequest): Promise<InviteUserResponse> {
    const response = await httpService.post<InviteUserResponse>(
      `${this.baseUrl}/users/invite`,
      data
    )
    return response.data
  }

  /**
   * Delete a user (if needed in future)
   */
  async deleteUser(userId: string): Promise<{ success: boolean; msg: string }> {
    const response = await httpService.delete<{ success: boolean; msg: string }>(
      `${this.baseUrl}/users/${userId}`
    )
    return response.data
  }

  /**
   * Update user role (if needed in future)
   */
  async updateUserRole(userId: string, role: string): Promise<{ success: boolean; msg: string }> {
    const response = await httpService.patch<{ success: boolean; msg: string }>(
      `${this.baseUrl}/users/${userId}/role`,
      { role }
    )
    return response.data
  }

  /**
   * Get user details by ID (if needed in future)
   */
  async getUserById(userId: string): Promise<User> {
    const response = await httpService.get<User>(`${this.baseUrl}/users/${userId}`)
    return response.data
  }
}

// Export singleton instance
const userManagementService = new UserManagementService()
export default userManagementService
