import httpBase, { RequestCallbacks } from '../http/httpBase'

// Types for User Management
export interface User {
  id: string
  username: string
  full_name: string
  email: string
  role: 'admin' | 'supervisor' | 'agent'
  created_at: string
  last_login: string | null
  onboarding_completed: boolean
}

export interface UsersListResponse {
  data: User[]
  meta: {
    page: number
    limit: number
    total: number
    total_pages: number
  }
}

export interface InviteUserRequest {
  username: string
  role: 'admin' | 'supervisor' | 'agent'
}

export interface InviteUserResponse {
  registration_token: string
  success: boolean
  msg: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  role?: string
}

/**
 * User Management Service
 * Handles all user-related API operations for admin dashboard
 */
class UserManagementService {
  /**
   * Get list of users with pagination and filtering
   */
  async getUsers(
    params: PaginationParams = {},
    callbacks?: RequestCallbacks<UsersListResponse>
  ): Promise<UsersListResponse> {
    const queryParams = new URLSearchParams()

    if (params.page) queryParams.append('page', params.page.toString())
    if (params.limit) queryParams.append('limit', params.limit.toString())
    if (params.search) queryParams.append('search', params.search)
    if (params.role) queryParams.append('role', params.role)

    const url = `/auth/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`

    const response = await httpBase.get<UsersListResponse>(url, {}, callbacks)
    return response.data
  }

  /**
   * Invite a new user
   */
  async inviteUser(
    data: InviteUserRequest,
    callbacks?: RequestCallbacks<InviteUserResponse>
  ): Promise<InviteUserResponse> {
    const response = await httpBase.post<InviteUserResponse>(
      '/auth/users/invite',
      data,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      },
      callbacks
    )
    return response.data
  }

  /**
   * Delete a user (if needed in future)
   */
  async deleteUser(
    userId: string,
    callbacks?: RequestCallbacks<{ success: boolean; msg: string }>
  ): Promise<{ success: boolean; msg: string }> {
    const response = await httpBase.delete<{ success: boolean; msg: string }>(
      `/auth/users/${userId}`,
      {},
      callbacks
    )
    return response.data
  }
}

// Export singleton instance
const userManagementService = new UserManagementService()
export default userManagementService
